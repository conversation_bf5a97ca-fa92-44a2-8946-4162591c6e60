'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import DynamicNavigation from './DynamicNavigation';

const Header = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <header className={`bg-white/95 backdrop-blur-sm shadow-md transition-all duration-500 fixed top-0 left-0 right-0 z-50 ${isVisible ? 'header-visible' : 'header-hidden'}`}>
      {/* Logo and Navigation */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center flex-shrink-0">
            <div className="text-2xl font-bold text-gray-800 hover:text-blue-600 transition-colors duration-300">
              等离子科技
            </div>
          </Link>

          {/* Navigation */}
          <DynamicNavigation className="hidden lg:flex items-center justify-center flex-1" />

          {/* 咨询按钮 */}
          <div className="hidden lg:flex items-center">
            <Link
              href="/contact-us"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-300 transform hover:scale-105 shadow-md hover:shadow-lg"
            >
              立即咨询
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button className="lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors duration-300 flex-shrink-0">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
