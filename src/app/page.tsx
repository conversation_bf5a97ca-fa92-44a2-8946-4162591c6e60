import Layout from '@/components/Layout';
import Link from 'next/link';
import WeChatButton from '@/components/WeChatButton';

export default function Home() {
  return (
    <Layout>

      {/* Hero Banner */}
      <section className="hero-banner relative min-h-screen bg-blue-50 text-gray-800 overflow-hidden flex items-center">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-blue-100"></div>

        <div className="relative container mx-auto px-4 py-20 z-10">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Left Content */}
              <div className="space-y-8">
                <div className="space-y-6">
                  <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold leading-tight text-gray-800">
                    专业的等离子清洗技术
                  </h1>
                  <p className="text-xl md:text-2xl text-gray-600 leading-relaxed">
                    为您的产品提供最先进的表面处理解决方案
                  </p>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-6">
                  <Link
                    href="/products"
                    className="btn-orange px-8 py-4 rounded-lg text-lg font-semibold transform hover:scale-105 shadow-lg hover:shadow-xl text-center"
                  >
                    了解更多
                  </Link>
                  <Link
                    href="/demo"
                    className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 px-8 py-4 rounded-lg text-lg font-medium transition-all duration-300 text-center"
                  >
                    3D演示
                  </Link>
                </div>
              </div>

              {/* Right Hero Image */}
              <div className="relative">
                <div className="w-full h-96 bg-gray-200 rounded-xl shadow-2xl overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-6xl mb-4">⚡</div>
                      <p className="text-gray-600 text-lg">等离子清洗设备展示</p>
                    </div>
                  </div>
                </div>
                
                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 w-20 h-20 bg-blue-200 rounded-full opacity-60 animate-float"></div>
                <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-orange-200 rounded-full opacity-60 animate-float" style={{ animationDelay: '1s' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-500 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="scroll-section py-24 bg-white relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 mb-8">
                我们的核心优势
              </h2>
              <p className="text-xl md:text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto">
                专业的等离子清洗技术顾问，专注于等离子表面处理技术咨询，为客户提供专业的技术指导和设备选型建议
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                { 
                  icon: "⚡", 
                  title: "先进技术", 
                  desc: "采用最新的等离子清洗技术，确保产品表面处理效果达到最高标准" 
                },
                { 
                  icon: "🏆", 
                  title: "专业服务", 
                  desc: "多年技术经验，为客户提供专业的技术咨询和设备选型建议" 
                },
                { 
                  icon: "🛠️", 
                  title: "技术支持", 
                  desc: "全程技术指导，从设备选型到安装调试，提供完善的技术支持" 
                },
                { 
                  icon: "🎯", 
                  title: "定制方案", 
                  desc: "根据客户具体需求，提供个性化的等离子清洗解决方案" 
                },
                { 
                  icon: "🔬", 
                  title: "质量保证", 
                  desc: "严格的质量控制体系，确保每个产品都达到行业最高标准" 
                },
                { 
                  icon: "📞", 
                  title: "售后服务", 
                  desc: "7×24小时服务热线，专业团队提供及时的技术支持和维护服务" 
                }
              ].map((feature, index) => (
                <div
                  key={index}
                  className="feature-card bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 text-center group border border-gray-100"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="text-4xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    {feature.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-blue-50 rounded-full -translate-y-1/2 translate-x-1/2 opacity-50"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-indigo-50 rounded-full translate-y-1/2 -translate-x-1/2 opacity-50"></div>
      </section>

      {/* Products Section */}
      <section className="scroll-section py-24 bg-gradient-to-br from-gray-50 to-blue-50 relative">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <span className="inline-block bg-blue-100 text-blue-600 px-6 py-3 rounded-full text-lg font-medium mb-6">
                产品系列
              </span>
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-800 mb-8">
                等离子清洗机设备
              </h2>
              <p className="text-xl md:text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto">
                专业设计各类等离子清洗机，满足不同行业需求
              </p>
            </div>

            {/* Product Categories */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {[
                {
                  name: '小型真空等离子清洗机',
                  desc: '适用于实验室和小批量生产',
                  icon: '🔬',
                  href: '/products/small-vacuum',
                  gradient: 'from-blue-500 to-cyan-500'
                },
                {
                  name: '大型真空等离子清洗机',
                  desc: '工业级大批量生产设备',
                  icon: '🏭',
                  href: '/products/large-vacuum',
                  gradient: 'from-purple-500 to-pink-500'
                },
                {
                  name: '大气等离子清洗机',
                  desc: '常压下高效表面处理',
                  icon: '⚡',
                  href: '/products/atmospheric',
                  gradient: 'from-green-500 to-teal-500'
                }
              ].map((product, index) => (
                <Link key={index} href={product.href} className="group">
                  <div className="product-card bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 relative overflow-hidden">
                    <div className={`absolute inset-0 bg-gradient-to-br ${product.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>
                    <div className="relative z-10">
                      <div className="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                        {product.icon}
                      </div>
                      <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors">
                        {product.name}
                      </h3>
                      <p className="text-gray-600 leading-relaxed mb-6">
                        {product.desc}
                      </p>
                      <div className="flex items-center text-blue-600 font-medium group-hover:translate-x-2 transition-transform duration-300">
                        了解详情
                        <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            <div className="text-center">
              <Link
                href="/products"
                className="inline-flex items-center gap-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white px-8 py-4 rounded-full text-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                查看全部产品
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </section>



      {/* Contact Section */}
      <section className="scroll-section py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block bg-white/20 text-white px-6 py-3 rounded-full text-lg font-medium mb-8 backdrop-blur-sm">
              联系我们
            </span>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8">
              开启合作之旅
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 leading-relaxed mb-16 max-w-3xl mx-auto">
              专业的管理团队、技术团队、维修团队，为您提供完善的售后服务
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {[
                { icon: "👤", label: "联系人", value: "曾先生" },
                { icon: "📱", label: "手机", value: "18954901489" },
                { icon: "💬", label: "微信", value: "wxid_gz1xribynia322" },
                { icon: "📧", label: "邮箱", value: "<EMAIL>" },
                { icon: "📍", label: "地址", value: "南京市江宁区" },
                { icon: "🔥", label: "服务热线", value: "18954901489", highlight: true },
                { icon: "🕒", label: "服务时间", value: "7×24小时" }
              ].map((contact, index) => (
                <div
                  key={index}
                  className={`contact-card bg-white/10 backdrop-blur-sm p-6 rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 ${contact.highlight ? 'ring-2 ring-yellow-300' : ''}`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className="text-3xl mb-4">{contact.icon}</div>
                  <div className="text-blue-200 text-sm mb-2">{contact.label}</div>
                  <div className={`font-semibold text-lg ${contact.highlight ? 'text-yellow-300' : 'text-white'}`}>
                    {contact.value}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <WeChatButton />
              <Link
                href="/demo"
                className="inline-flex items-center gap-3 bg-transparent border-2 border-white text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-white hover:text-blue-600 transition-all duration-300 transform hover:scale-105"
              >
                体验3D演示
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-white/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
        <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>
      </section>

    </Layout>
  );
}
