import type { Metadata } from "next";
import { Inter } from "next/font/google";

import "./globals.css";

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

export const metadata: Metadata = {
  title: "等离子科技 - 专业的等离子清洗技术",
  description: "专业的等离子清洗技术咨询与设备推荐服务，为您的产品提供最先进的表面处理解决方案",
  keywords: "等离子清洗,等离子技术,表面处理,等离子设备,等离子清洗机",
  authors: [{ name: "等离子科技" }],
  viewport: "width=device-width, initial-scale=1",
  robots: "index, follow",
  openGraph: {
    title: "等离子科技 - 专业的等离子清洗技术",
    description: "专业的等离子清洗技术咨询与设备推荐服务",
    type: "website",
    locale: "zh_CN",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* 51la网站统计 - 仅在生产环境加载 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 检查是否为生产环境
              if (typeof window !== 'undefined' &&
                  window.location.hostname !== 'localhost' &&
                  window.location.hostname !== '127.0.0.1') {

                // 动态加载51la统计脚本
                var script = document.createElement('script');
                script.charset = 'UTF-8';
                script.id = 'LA_COLLECT';
                script.src = '//sdk.51.la/js-sdk-pro.min.js';
                script.async = true;

                script.onload = function() {
                  if (typeof LA !== 'undefined') {
                    LA.init({
                      id: "3MxL2unzxXS4DKRH",
                      ck: "3MxL2unzxXS4DKRH",
                      autoTrack: true,
                      screenRecord: true
                    });
                  }
                };

                script.onerror = function() {
                  console.log('51la统计脚本加载失败');
                };

                document.head.appendChild(script);
              }
            `,
          }}
        />
      </head>
      <body
        className={`${inter.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
